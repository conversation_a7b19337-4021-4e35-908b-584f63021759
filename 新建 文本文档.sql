-- DROP FUNCTION public.af_pda_wms_sales_outbound_picking(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_picking(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能   ： 销售出库拣货
 * 描述   ：
 * 时间   ：
 * 开发者 ：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_bill_id text;
		_sn_no text;
		_part_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;
	
		_sn_type text;
		_produce_date date;
		_produce_date_min date;
	
		sub_datas record;
		_prod_lot text;
		_b_id text;
		_invp_area text;
		_client_no text;
		_client_part_no text;
		_prod_cycle text;
		_prod_cycle_min text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	begin
		json_datas := json(datas);	
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';
		--_part_no := json_datas->>'part_no';	
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		select part_no,part_qty,sn_type,inventory_lot,produce_date::date,invp_area_no into _part_no,_part_qty,_sn_type,_prod_lot,_produce_date,_invp_area  
		from wm_sn where sn_no=_sn_no;
	
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
			_err_msg := format('扫描销售出库单【%s】，不是待拣货状态。',_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		select cr_dlv_h_id,client_no into _bill_id,_client_no from cr_dlv_h where cr_dlv_h_no=_bill_no;

		/*
		if _client_no='3031' or _client_no='3039' then
			_client_part_no := substring(split_part(_sn_no,'/',1),3);
			if substring(_part_no,length(_client_part_no)+2,4)<> _client_no then
				_part_no := substring(_part_no,1,length(_client_part_no)+1)||_client_no||substring(_part_no,length(_client_part_no)+6);
			end if;
		end if;
		*/

        --2025.7.9 何光品添加
       	if exists(select 1 from wm_sn where sn_no=_sn_no and invp_area_no='W12') then
            _err_msg := format('条码：【%s】，还在W12仓不可拣货！', _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
        end if;
        	

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】仓库【%s】）不属于此销售出库单【%s】限定的产品与仓库，不能拣货。',_part_no,_sn_no,_invp_area,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);		
		end if;
	
		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='800') then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】）不是在库状态，不能拣货!!',_part_no,_sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
		
		----------------------------------------------------------------------------------------
		if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
			select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
			--if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and produce_date::date<_produce_date and sn_status='800') then
			if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
				--select min(produce_date)::date into _produce_date_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';
				select min(substring(split_part(_sn_no,'/021748',2),1,4)) into _prod_cycle_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';				
				_err_msg := format('扫描产品（物料编码【%s】条码【%s】日期【%s】），存在更早日期的产品【%s】，不能拣货。',_part_no,_sn_no,_prod_cycle,_prod_cycle_min);
				res := row('false', _err_msg);
				return to_json(res);
			end if;
		end if;
		----------------------------------------------------------------------------------------
	
		if exists(select 1 from cr_dlv_sn_part where sn_no=_sn_no) then
			res := row('false', '扫描产品条码已经拣货，不能二次拣货。');
			return to_json(res);
		end if;
	
		select sum(part_qty) into _part_qty_real from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
		select sum(cr_dlv_qty_plan) into _part_qty_plan from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
		if _part_qty_plan < coalesce(_part_qty_real,0)+_part_qty then
			_err_msg := format('产品（物料编码【%s】）拣货数量(已拣货数量【%s】+现拣货数量【%s】)大于销售出库单【%s】需要求数量【%s】，不能拣货。',_part_no,coalesce(_part_qty_real,0),_part_qty::int,_bill_no,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		---------------------------------------------------------------------------------------
        --检查同一出库单中，相同物料、相同仓库是否存在多行明细,如果只有一行，则走简单的单行处理逻辑
		if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area having count(*)>1) then
			   
               --遍历所有相关的明细行,每行明细都有独立的计划数量和已拣数量
               for sub_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) loop
				 
                  --剩余需求检查：cr_dlv_qty_plan-cr_dlv_qty >0 确保当前行还有未满足的需求、拣货数量检查：_part_qty>0 确保还有待分配的拣货数量
                  if sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty >0 and _part_qty>0 then	
					
                     if (sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty)>=_part_qty then
						update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := 0;
					else
						update public.cr_dlv_b set cr_dlv_qty=sub_datas.cr_dlv_qty_plan
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := _part_qty+sub_datas.cr_dlv_qty-sub_datas.cr_dlv_qty_plan;
					end if;
				   end if;

			end loop;
		else
			select cr_dlv_b_id into _b_id from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
			insert into public.cr_dlv_sn_part
			(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', _b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from pd_part
			where part_no=_part_no;

			update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
			where cr_dlv_b_id=_b_id;
		end if;
		---------------------------------------------------------------------------------------

		update public.cr_dlv_b set cr_dlv_b_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id and part_no=_part_no and cr_dlv_qty_plan=cr_dlv_qty;
		
		---------------------------------------------------------------------------------------

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_b_rmk6,'')='') then
			update public.cr_dlv_h set cr_dlv_h_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where cr_dlv_h_id=_bill_id; 

			update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
			from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
				from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
				left join (select part_no,count(sn_no) as ctn_num from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id) group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
				where tmp1.rn=1) tt
			where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
		end if;
	
		update public.wm_sn set sn_status='810',sn_status_name='拣货',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;
       
    		
       
		---------------------------------------------------------------------------------------
		res := row('true', '销售出库拣货完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;
